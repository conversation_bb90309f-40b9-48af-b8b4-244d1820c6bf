class SlotMachine {
    constructor() {
        this.symbols = ['🍒', '🍋', '🍊', '🍇', '🍎', '🥝'];
        this.score = 1000;
        this.bet = 10;
        this.minBet = 5;
        this.maxBet = 100;
        this.spinning = false;
        
        this.reels = [
            document.getElementById('reel1'),
            document.getElementById('reel2'),
            document.getElementById('reel3')
        ];
        
        this.scoreDisplay = document.getElementById('score');
        this.betDisplay = document.getElementById('bet');
        this.resultMessage = document.getElementById('result-message');
        this.spinButton = document.getElementById('spin-btn');
        this.betMinusButton = document.getElementById('bet-minus');
        this.betPlusButton = document.getElementById('bet-plus');
        
        this.initializeEvents();
        this.updateDisplays();
        this.initializeReels();
    }
    
    initializeEvents() {
        this.spinButton.addEventListener('click', () => this.spin());
        this.betMinusButton.addEventListener('click', () => this.adjustBet(-5));
        this.betPlusButton.addEventListener('click', () => this.adjustBet(5));
        
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !this.spinning) {
                e.preventDefault();
                this.spin();
            }
        });
    }
    
    initializeReels() {
        this.reels.forEach(reel => {
            const symbolElement = reel.querySelector('.symbol');
            symbolElement.textContent = this.getRandomSymbol();
        });
    }
    
    getRandomSymbol() {
        return this.symbols[Math.floor(Math.random() * this.symbols.length)];
    }
    
    adjustBet(amount) {
        if (this.spinning) return;
        
        const newBet = this.bet + amount;
        if (newBet >= this.minBet && newBet <= this.maxBet && newBet <= this.score) {
            this.bet = newBet;
            this.updateDisplays();
        }
    }
    
    updateDisplays() {
        this.scoreDisplay.textContent = this.score;
        this.betDisplay.textContent = this.bet;
        
        this.betMinusButton.disabled = this.bet <= this.minBet;
        this.betPlusButton.disabled = this.bet >= this.maxBet || this.bet >= this.score;
        
        if (this.score < this.minBet) {
            this.spinButton.disabled = true;
            this.resultMessage.textContent = '分數不足！遊戲結束！';
            this.resultMessage.style.color = '#ff4444';
        } else {
            this.spinButton.disabled = this.spinning;
        }
    }
    
    async spin() {
        if (this.spinning || this.score < this.bet) return;
        
        this.spinning = true;
        this.score -= this.bet;
        this.updateDisplays();
        
        this.spinButton.disabled = true;
        this.resultMessage.textContent = '轉動中...';
        this.resultMessage.style.color = '#ffd700';
        
        this.reels.forEach(reel => {
            reel.classList.remove('winning');
            reel.classList.add('spinning');
        });
        
        const results = [];
        const spinDurations = [1500, 2000, 2500];
        
        const spinPromises = this.reels.map((reel, index) => {
            return new Promise(resolve => {
                setTimeout(() => {
                    const finalSymbol = this.getRandomSymbol();
                    results[index] = finalSymbol;
                    
                    reel.classList.remove('spinning');
                    const symbolElement = reel.querySelector('.symbol');
                    symbolElement.textContent = finalSymbol;
                    
                    resolve();
                }, spinDurations[index]);
            });
        });
        
        await Promise.all(spinPromises);
        
        const winAmount = this.calculateWin(results);
        this.score += winAmount;
        
        this.showResult(results, winAmount);
        this.updateDisplays();
        
        setTimeout(() => {
            this.spinning = false;
            this.updateDisplays();
        }, 1000);
    }
    
    calculateWin(results) {
        const [first, second, third] = results;
        
        if (first === second && second === third) {
            switch (first) {
                case '🍒': return this.bet * 20;
                case '🍋': return this.bet * 15;
                case '🍊': return this.bet * 10;
                case '🍇': return this.bet * 8;
                case '🍎': return this.bet * 6;
                case '🥝': return this.bet * 4;
            }
        }
        
        if (first === second || second === third || first === third) {
            return this.bet * 2;
        }
        
        return 0;
    }
    
    showResult(results, winAmount) {
        if (winAmount > 0) {
            this.resultMessage.textContent = `恭喜中獎！獲得 ${winAmount} 分！`;
            this.resultMessage.style.color = '#00ff00';
            
            this.reels.forEach(reel => {
                reel.classList.add('winning');
            });
            
            this.playWinAnimation();
        } else {
            this.resultMessage.textContent = '沒有中獎，再試一次！';
            this.resultMessage.style.color = '#ff6b35';
        }
    }
    
    playWinAnimation() {
        const colors = ['#ff6b35', '#ffd700', '#00ff00', '#ff1744'];
        let colorIndex = 0;
        
        const interval = setInterval(() => {
            this.resultMessage.style.color = colors[colorIndex];
            colorIndex = (colorIndex + 1) % colors.length;
        }, 200);
        
        setTimeout(() => {
            clearInterval(interval);
            this.resultMessage.style.color = '#00ff00';
        }, 2000);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const game = new SlotMachine();
    
    const instructions = document.createElement('div');
    instructions.innerHTML = `
        <div style="position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); 
                    padding: 15px; border-radius: 10px; color: #ffd700; font-size: 0.9em;
                    border: 2px solid #ffd700; max-width: 200px;">
            <strong>操作說明：</strong><br>
            • 點擊按鈕調整下注金額<br>
            • 按空白鍵或點擊按鈕開始遊戲<br>
            • 三個相同符號獲得大獎<br>
            • 兩個相同符號獲得小獎
        </div>
    `;
    document.body.appendChild(instructions);
});