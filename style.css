* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
}

.game-container {
    max-width: 800px;
    width: 100%;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    color: #ffd700;
}

header p {
    font-size: 1.2em;
    color: #e0e0e0;
}

.slot-machine {
    background: linear-gradient(145deg, #8B4513, #A0522D);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 5px solid #DAA520;
    margin-bottom: 20px;
}

.display-panel {
    background: #000;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    border: 2px solid #ffd700;
}

.score-display, .bet-display {
    font-size: 1.4em;
    font-weight: bold;
    color: #00ff00;
    text-shadow: 0 0 10px #00ff00;
}

.reels-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    padding: 20px;
    background: #222;
    border-radius: 15px;
    border: 3px solid #ffd700;
}

.reel {
    width: 120px;
    height: 120px;
    background: #fff;
    border: 4px solid #333;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(0,0,0,0.2);
}

.reel.spinning {
    animation: spin 2s linear;
}

@keyframes spin {
    0% { transform: translateY(0); }
    25% { transform: translateY(-100px); }
    50% { transform: translateY(-200px); }
    75% { transform: translateY(-100px); }
    100% { transform: translateY(0); }
}

.symbol {
    font-size: 4em;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.reel.winning .symbol {
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    100% { transform: translate(-50%, -50%) scale(1.2); }
}

.control-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.bet-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(0,0,0,0.3);
    padding: 10px 20px;
    border-radius: 25px;
    border: 2px solid #ffd700;
}

.bet-controls button {
    width: 40px;
    height: 40px;
    border: none;
    background: #ff6b35;
    color: white;
    font-size: 1.5em;
    font-weight: bold;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bet-controls button:hover {
    background: #ff5722;
    transform: scale(1.1);
}

.bet-controls button:active {
    transform: scale(0.95);
}

.bet-controls span {
    font-size: 1.2em;
    font-weight: bold;
    color: #ffd700;
}

.spin-button {
    width: 200px;
    height: 60px;
    background: linear-gradient(145deg, #ff6b35, #ff5722);
    border: none;
    border-radius: 30px;
    color: white;
    font-size: 1.4em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.spin-button:hover {
    background: linear-gradient(145deg, #ff5722, #e64a19);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.spin-button:active {
    transform: translateY(0);
}

.spin-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.info-display {
    background: rgba(0,0,0,0.5);
    padding: 15px 25px;
    border-radius: 20px;
    border: 2px solid #ffd700;
    min-width: 300px;
    text-align: center;
}

#result-message {
    font-size: 1.2em;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.paytable {
    background: rgba(0,0,0,0.7);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #ffd700;
}

.paytable h3 {
    text-align: center;
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.payout-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 15px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-size: 1.1em;
}

.payout-row span:first-child {
    font-size: 1.3em;
}

.payout-row span:last-child {
    color: #ffd700;
    font-weight: bold;
}

@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .slot-machine {
        padding: 20px;
    }
    
    .reels-container {
        gap: 10px;
    }
    
    .reel {
        width: 90px;
        height: 90px;
    }
    
    .symbol {
        font-size: 3em;
    }
    
    .spin-button {
        width: 150px;
        height: 50px;
        font-size: 1.2em;
    }
}